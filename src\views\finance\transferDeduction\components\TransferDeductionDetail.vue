<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitchDetail(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchDetail(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="delete" @click="handleDelete">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h4 class="text-[18px] font-bold mr-[12px]">转款抵扣详情</h4>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_BaseStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <div id="basic" class="mb-[40px]">
        <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">基础信息</h4>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">业务时间：{{ detailData.bizDate || '-' }}</span>
          <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
          <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
          <span class="w-[50%]">审核人：{{ detailData.auditBy_dictText || '-' }}</span>
          <span class="w-[50%]">审核时间：{{ detailData.auditTime || '-' }}</span>
          <span class="w-[50%]">转款抵扣金额：{{ detailData.transferDeductionAmount || '-' }}</span>
          <span class="w-[100%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
      </div>

      <div id="transferItems" class="mb-[40px]">
        <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">转款明细</h4>
        <a-empty v-if="!transferList.length" description="暂无数据"></a-empty>
        <a-table
          v-else
          :columns="transferColumns"
          :data-source="transferList"
          :scroll="{ x: 1000 }"
          :pagination="false"
        />
      </div>

      <div id="deductionItems" class="mb-[40px]">
        <h4 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">抵扣欠款明细</h4>
        <a-empty v-if="!deductionList.length" description="暂无数据"></a-empty>
        <a-table
          v-else
          :columns="deductionColumns"
          :data-source="deductionList"
          :scroll="{ x: 1000 }"
          :pagination="false"
        />
      </div>
    </a-spin>
  </a-drawer>

  <edit-transfer-deduction ref="editDrawerRef" @refresh="refreshData" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import {
  getTransferDeductionById,
  deleteTransferDeduction,
  getTransferDeductionDetail,
  getDeductionDetail
} from '../apis'
import EditTransferDeduction from './EditTransferDeduction.vue'

const props = defineProps({
  dataList: { type: Array, required: true }
})

const emits = defineEmits(['refresh'])

const visible = ref(false)
const loading = ref(false)
const detailData = ref({})
const transferList = ref([])
const deductionList = ref([])
const editDrawerRef = ref()

const currentIndex = computed(() => {
  if (!detailData.value.id) return 0
  return props.dataList.findIndex((i) => i.id === detailData.value.id)
})

/**
 * 转款明细表格列配置
 */
const transferColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 160, fixed: 'left' },
  { title: '客户', dataIndex: 'customer', width: 160, ellipsis: true },
  { title: '合同', dataIndex: 'contract', width: 160 },
  { title: '款项类型', dataIndex: 'paymentType', width: 120 },
  { title: '归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '减免金额', dataIndex: 'remission', width: 120 },
  { title: '实际应收', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '未收金额', dataIndex: 'residual', width: 120 },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', width: 120 },
  { title: '已退金额', dataIndex: 'refunded', width: 120 },
  { title: '已处理尾差', dataIndex: 'offDifference', width: 120 },
  { title: '剩余可转', dataIndex: 'residueTransferAmount', width: 120, fixed: 'right' },
  { title: '本次转款金额', dataIndex: 'thisTransferOutAmount', width: 140, fixed: 'right' },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true, fixed: 'right' }
]

/**
 * 抵扣欠款明细表格列配置
 */
const deductionColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 160, fixed: 'left' },
  { title: '客户', dataIndex: 'customer', width: 160, ellipsis: true },
  { title: '合同', dataIndex: 'contract', width: 160 },
  { title: '款项类型', dataIndex: 'paymentType', width: 120 },
  { title: '归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '减免金额', dataIndex: 'remission', width: 120 },
  { title: '实际应收', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '未收金额', dataIndex: 'residual', width: 120 },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', width: 120 },
  { title: '已退金额', dataIndex: 'refunded', width: 120 },
  { title: '已处理尾差', dataIndex: 'offDifference', width: 120 },
  { title: '剩余可转', dataIndex: 'residueTransferAmount', width: 120, fixed: 'right' },
  { title: '本次转款金额', dataIndex: 'thisTransferOutAmount', width: 140, fixed: 'right' },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true, fixed: 'right' }
]

/**
 * 打开详情抽屉
 * @param {Object} record - 转款抵扣记录数据
 */
const open = async (record) => {
  if (!record || !record.id) {
    message.error('缺少必要参数')
    return
  }
  visible.value = true
  await loadDetail(record.id)
}

/**
 * 关闭详情抽屉
 */
const handleCancel = () => {
  emits('refresh')
  visible.value = false
}

/**
 * 打开编辑抽屉
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 处理删除转款抵扣记录
 */
const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除单据 ${detailData.value.number} 吗？`,
    onOk: async () => {
      await deleteTransferDeduction({ id: detailData.value.id })
      message.success('删除成功')
      handleCancel()
    }
  })
}

/**
 * 刷新当前详情数据
 */
const refreshData = async () => {
  if (detailData.value.id) {
    await loadDetail(detailData.value.id)
    emits('refresh')
  }
}

/**
 * 加载详情数据
 * @param {string} id - 转款抵扣记录ID
 */
const loadDetail = async (id) => {
  loading.value = true

  try {
    const [basicResult, transferResult, deductionResult] = await Promise.all([
      getTransferDeductionById({ id }),
      getTransferDeductionDetail({ id }),
      getDeductionDetail({ id })
    ])

    detailData.value = basicResult.result
    transferList.value = transferResult.result || []
    deductionList.value = deductionResult.result || []
  } finally {
    loading.value = false
  }
}

/**
 * 切换详情记录
 * @param {number} index - 目标记录在列表中的索引
 */
const handleSwitchDetail = (index) => {
  if (index < 0 || index >= props.dataList.length) return
  loadDetail(props.dataList[index].id)
}

defineExpose({
  open
})
</script>
